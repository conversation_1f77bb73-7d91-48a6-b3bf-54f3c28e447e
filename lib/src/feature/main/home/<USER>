import 'package:elementary/elementary.dart';
import 'package:elementary_helper/elementary_helper.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:sba/src/feature/main/home/<USER>';
import 'package:sba/src/feature/main/home/<USER>/mot_data.dart';
import 'package:sba/src/feature/main/home/<USER>/mot_tile.dart';
import 'package:sba/src/feature/main/home/<USER>/toll_tile.dart';
import 'package:sba/src/feature/main/home/<USER>/vehicle_tile.dart';
import 'package:sba/src/feature/main/model/destination_type.dart';
import 'package:sba/src/feature/subscription/details/widget/subscription_tile.dart';
import 'package:sba/src/generated/assets.gen.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/content/model/newspaper.dart';
import 'package:sba/src/repository/subscription/model/subscription_data.dart';
import 'package:sba/src/repository/toll/model/toll_data.dart';
import 'package:sba/src/repository/user/model/user_data.dart';
import 'package:sba/src/repository/vehicle/model/vehicle_data.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/widget/action_box.dart';
import 'package:sba/src/ui/widget/navigation_box.dart';
import 'package:sba/src/ui/widget/sliver_subtitle.dart';
import 'package:sba/src/ui/widget/sliver_title.dart';
import 'package:sba/src/ui/widget/smart_carousel.dart';
import 'package:sba/src/ui/widget/smart_list_view.dart';
import 'package:sliver_tools/sliver_tools.dart';

class HomeScreen extends ElementaryWidget<IHomeWidgetModel> {
  const HomeScreen({
    Key? key,
    WidgetModelFactory wmFactory = defaultHomeWidgetModelFactory,
  }) : super(wmFactory, key: key);

  @override
  Widget build(IHomeWidgetModel wm) {
    return Scaffold(
      body: NestedScrollView(
        headerSliverBuilder: (context, _) => [
          DoubleSourceBuilder(
            firstSource: wm.userData,
            secondSource: wm.vehicles,
            builder: (context, data, vehicles) => SliverTitle(
              text: context.l10n.home_title(
                data?.safeName ?? context.l10n.home_title_guest,
              ),
              action: data == null || (vehicles?.isEmpty ?? true)
                  ? null
                  : FloatingActionButton.small(
                      onPressed: wm.onAddVehicleTap,
                      child: const Icon(Icons.add),
                    ),
              padding: UISpacing.defaultElementPaddingWithoutBottom,
            ),
          ),
        ],
        body: Builder(
          builder: (context) {
            return CustomScrollView(
              slivers: [
                const SliverGap(UISpacing.xxl),
                StateNotifierBuilder(
                  listenableState: wm.warningMessage,
                  builder: (context, message) => SliverToBoxAdapter(
                    child: message != null
                        ? Padding(
                            padding: UISpacing.defaultElementPaddingWithoutTop,
                            child: message,
                          )
                        : const SizedBox.shrink(),
                  ),
                ),
                DoubleSourceBuilder(
                  firstSource: wm.userData,
                  secondSource: wm.vehicles,
                  builder: (context, user, vehicles) => SliverToBoxAdapter(
                    child: user != null && (vehicles?.isEmpty ?? false)
                        ? Padding(
                            padding: UISpacing.defaultElementPaddingWithoutTop,
                            child: ActionBox(
                              icon: Icons.add,
                              text: context.l10n.action_add_vehicle,
                              onTap: wm.onAddVehicleTap,
                            ),
                          )
                        : const SizedBox.shrink(),
                  ),
                ),
                StateNotifierBuilder(
                  listenableState: wm.vehicles,
                  builder: (context, data) => SmartCarousel<VehicleData>.sliver(
                    loading: data == null,
                    skeletonData: [VehicleData.fake()],
                    data: data ?? [],
                    height: 250,
                    padding: const EdgeInsets.only(bottom: UISpacing.xxl),
                    builder: (context, data) => VehicleTile(data: data),
                    onSelectionChange: wm.onVehicleChange,
                  ),
                ),
                StateNotifierBuilder(
                  listenableState: wm.selectedVehicle,
                  builder: (context, data) => SliverVisibility(
                    visible: data != null,
                    sliver: _VehicleData(
                      wm: wm,
                      vehicle: data,
                    ),
                  ),
                ),
                const SliverGap(UISpacing.l),
                DoubleSourceBuilder(
                  firstSource: wm.destinations,
                  secondSource: wm.newspaper,
                  builder: (context, destinations, newspaper) => SliverPadding(
                    padding: UISpacing.defaultElementPaddingWithoutTop,
                    sliver: SliverGrid.builder(
                      gridDelegate:
                          const SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 3,
                        mainAxisSpacing: UISpacing.s,
                        crossAxisSpacing: UISpacing.s,
                      ),
                      itemCount: (destinations?.length ?? 0) +
                          (newspaper != null ? 1 : 0),
                      itemBuilder: (context, i) {
                        final data = destinations?.elementAtOrNull(i);
                        if (data == null) {
                          return NavigationBox(
                            icon: Assets.icons.twoPager.svg(),
                            text: newspaper!.localizedTitle(context),
                            onTap: () => wm.onNewspaperTap(newspaper),
                          );
                        }
                        return NavigationBox(
                          icon: data.type.icon,
                          text: data.type.localizedName(context),
                          enabled: data.enabled,
                          onTap: () => wm.onDestinationTap(data),
                        );
                      },
                    ),
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }
}

class _VehicleData extends StatelessWidget {
  const _VehicleData({required this.wm, this.vehicle});

  final IHomeWidgetModel wm;
  final VehicleData? vehicle;

  @override
  Widget build(BuildContext context) {
    return SliverPadding(
      padding: UISpacing.defaultElementPaddingWithoutTop,
      sliver: SliverAnimatedSwitcher(
        duration: const Duration(milliseconds: 700),
        child: MultiSliver(
          key: ValueKey(vehicle),
          children: [
            SliverSubtitle(
              text: context.l10n.home_section_subscription,
            ),
            const SliverGap(UISpacing.l),
            ValueListenableBuilder(
              valueListenable: wm.subscription,
              builder: (context, state, _) => SmartListView.sliver(
                loading: state.isLoadingState,
                data: state.data == null ? null : [state.data!],
                skeletonData: [SubscriptionData.fake()],
                builder: (context, data) => SubscriptionTile(
                  data: data,
                  onTap: () => wm.onSubscriptionTap(data),
                ),
                emptyText:
                    context.l10n.empty_subscription(vehicle?.plateNumber ?? ''),
                centerEmptyText: false,
              ),
            ),
            const SliverGap(UISpacing.xxl),
            SliverSubtitle(text: context.l10n.home_section_toll),
            const SliverGap(UISpacing.l),
            ValueListenableBuilder(
              valueListenable: wm.toll,
              builder: (context, state, _) => SmartListView.sliver(
                loading: state.isLoadingState,
                data: state.data == null ? null : [state.data!],
                skeletonData: [TollData.fake()],
                builder: (context, data) => TollTile(data: data),
                emptyText: context.l10n.empty_toll(vehicle?.plateNumber ?? ''),
                centerEmptyText: false,
              ),
            ),
            const SliverGap(UISpacing.l),
            SliverToBoxAdapter(
              child: FilledButton(
                onPressed: wm.onTollTap,
                child: Text(context.l10n.action_buy_toll),
              ),
            ),
            const SliverGap(UISpacing.xxl),
            SliverSubtitle(text: context.l10n.home_section_mot),
            const SliverGap(UISpacing.l),
            ValueListenableBuilder(
              valueListenable: wm.mot,
              builder: (context, state, _) => SmartListView.sliver(
                loading: state.isLoadingState,
                data: state.data == null ? null : [state.data!],
                skeletonData: [MotData.fake()],
                builder: (context, data) => MotTile(data: data),
                emptyText: context.l10n.empty_mot(vehicle?.plateNumber ?? ''),
                centerEmptyText: false,
              ),
            ),
            const SliverGap(UISpacing.l),
            SliverToBoxAdapter(
              child: FilledButton(
                onPressed: wm.onMotTap,
                child: Text(context.l10n.action_reserve_mot),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
