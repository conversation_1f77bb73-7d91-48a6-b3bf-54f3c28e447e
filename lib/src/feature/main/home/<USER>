import 'package:elementary/elementary.dart';
import 'package:elementary_helper/elementary_helper.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:rxdart/rxdart.dart';
import 'package:sba/src/app/di/di.dart';
import 'package:sba/src/common/extension/build_context_extension.dart';
import 'package:sba/src/common/external_launcher/external_launcher.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/feature/auth/auth_router.dart';
import 'package:sba/src/feature/main/home/<USER>';
import 'package:sba/src/feature/main/home/<USER>';
import 'package:sba/src/feature/main/home/<USER>/mot_data.dart';
import 'package:sba/src/feature/main/main_router.dart';
import 'package:sba/src/feature/main/model/destination.dart';
import 'package:sba/src/feature/main/model/destination_type.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/content/model/newspaper.dart';
import 'package:sba/src/repository/subscription/model/subscription_data.dart';
import 'package:sba/src/repository/toll/model/toll_data.dart';
import 'package:sba/src/repository/user/model/type/user_type.dart';
import 'package:sba/src/repository/user/model/user_data.dart';
import 'package:sba/src/repository/vehicle/model/vehicle_data.dart';
import 'package:sba/src/ui/widget/message_box.dart';

abstract interface class IHomeWidgetModel implements IWidgetModel {
  StateNotifier<UserData?> get userData;

  StateNotifier<List<VehicleData>> get vehicles;

  StateNotifier<List<Destination>> get destinations;

  StateNotifier<Widget?> get warningMessage;

  StateNotifier<VehicleData?> get selectedVehicle;

  StateNotifier<Newspaper?> get newspaper;

  EntityStateNotifier<SubscriptionData?> get subscription;

  EntityStateNotifier<MotData?> get mot;

  EntityStateNotifier<TollData?> get toll;

  void onAddVehicleTap();

  void onDestinationTap(Destination destination);

  void onVehicleChange(VehicleData data);

  void onSubscriptionTap(SubscriptionData data);

  void onTollTap();

  void onMotTap();

  void onNewspaperTap(Newspaper newspaper);
}

HomeWidgetModel defaultHomeWidgetModelFactory(BuildContext context) {
  return HomeWidgetModel(
    HomeModel(
      authRepository: get(),
      userRepository: get(),
      vehicleRepository: get(),
      errorHandler: get(),
      contentRepository: get(),
      tollRepository: get(),
      subscriptionRepository: get(),
    ),
  );
}

class HomeWidgetModel extends WidgetModel<HomeScreen, HomeModel>
    implements IHomeWidgetModel {
  HomeWidgetModel(super.model);

  final _userData = StateNotifier<UserData?>();
  final _vehicles = StateNotifier<List<VehicleData>>();
  final _destinations = StateNotifier<List<Destination>>(
    initValue: _normalDestinations,
  );
  final _warningMessage = StateNotifier<Widget?>();
  final _selectedVehicle = StateNotifier<VehicleData?>();
  final _subscription = EntityStateNotifier<SubscriptionData?>(
    EntityState.loading(),
  );
  final _mot = EntityStateNotifier<MotData?>(EntityState.loading());
  final _toll = EntityStateNotifier<TollData?>(EntityState.loading());
  final _newspaper = StateNotifier<Newspaper?>();
  final _sub = CompositeSubscription();

  @override
  void initWidgetModel() async {
    _sub
      ..add(
        model.user.listen(_userData.accept),
      )
      ..add(
        model.type
            .map(
              (type) => switch (type) {
                UserType.normal => null,
                UserType.guest => MessageBox(
                  type: MessageBoxType.error,
                  title: context.l10n.message_feature_locked_title,
                  text: context.l10n.message_feature_locked_text,
                  actionBuilder: (context, color) => [
                    TextButton(
                      onPressed: _onRegisterTap,
                      style: TextButton.styleFrom(foregroundColor: color),
                      child: Text(
                        context.l10n.action_register,
                      ),
                    ),
                    TextButton(
                      onPressed: _onLoginTap,
                      style: TextButton.styleFrom(foregroundColor: color),
                      child: Text(
                        context.l10n.action_login,
                      ),
                    ),
                  ],
                ),
              },
            )
            .listen(_warningMessage.accept),
      )
      ..add(
        CombineLatestStream.combine2(
              model.type,
              model.vehicles,
              (a, b) =>
                  (type: a, hasVehicles: b.maybeValue?.isNotEmpty ?? false),
            )
            .map(
              (data) => switch (data.type) {
                UserType.normal =>
                  data.hasVehicles
                      ? _normalDestinations
                      : _uncompletedDestinations,
                UserType.guest => _guestDestinations,
              },
            )
            .listen(_destinations.accept),
      )
      ..add(
        model.vehicles
            .asyncMap((result) async {
              if (result is Failure) {
                await context.showGeneralErrorDialog(
                  failure: result as Failure,
                );
              }

              return result;
            })
            .map((e) => e.maybeValue)
            .listen(_vehicles.accept),
      )
      ..add(
        model.vehicles
            .map((e) => e.maybeValue)
            .map((e) => e?.firstOrNull)
            .whereNotNull()
            .take(1)
            .doOnData(onVehicleChange)
            .publish()
            .connect(),
      )
      ..add(
        model.type
            .asyncMap(
              (type) => switch (type) {
                UserType.normal => model.newspaper,
                UserType.guest => null,
              },
            )
            .listen(_newspaper.accept),
      );

    super.initWidgetModel();
  }

  @override
  void dispose() {
    _sub.dispose();
    super.dispose();
  }

  Future<void> _onLoginTap() async {
    await model.logout();
  }

  Future<void> _onRegisterTap() async {
    await model.logout();
    const RegisterRoute().go(context);
  }

  @override
  void onAddVehicleTap() => const MyVehiclesRoute().go(context);

  @override
  void onDestinationTap(Destination destination) =>
      context.go(destination.type.path);

  @override
  void onVehicleChange(VehicleData data) async {
    _selectedVehicle.accept(data);
    _subscription.loading();
    _toll.loading();
    _mot.loading();

    _subscription.content(await model.subscriptionForVehicle(data));
    _toll.content(await model.validTollForVehicle(data));
    _mot.content(null);
  }

  @override
  void onSubscriptionTap(SubscriptionData data) {
    SubscriptionDetailsRoute(subscriptionId: data.id).push<void>(context);
  }

  @override
  void onMotTap() {}

  @override
  void onTollTap() {
    const BuyTollRoute().go(context);
  }

  @override
  void onNewspaperTap(Newspaper data) => ExternalLauncher.uri(data.uri);

  @override
  StateNotifier<List<Destination>> get destinations => _destinations;

  @override
  StateNotifier<UserData?> get userData => _userData;

  @override
  StateNotifier<List<VehicleData>> get vehicles => _vehicles;

  @override
  StateNotifier<Widget?> get warningMessage => _warningMessage;

  @override
  StateNotifier<VehicleData?> get selectedVehicle => _selectedVehicle;

  @override
  EntityStateNotifier<MotData?> get mot => _mot;

  @override
  EntityStateNotifier<SubscriptionData?> get subscription => _subscription;

  @override
  EntityStateNotifier<TollData?> get toll => _toll;

  static const _normalDestinations = [
    Destination(type: DestinationType.parking),
    Destination(type: DestinationType.mot),
    Destination(type: DestinationType.roadAssistance),
    Destination(type: DestinationType.subscription),
    Destination(type: DestinationType.toll),
    Destination(type: DestinationType.serviceBook),
    Destination(type: DestinationType.legalHelp),
    Destination(type: DestinationType.training),
    Destination(type: DestinationType.roadCameras),
    Destination(type: DestinationType.discounts),
    Destination(type: DestinationType.information),
  ];

  static const _uncompletedDestinations = [
    Destination(type: DestinationType.parking, enabled: false),
    Destination(type: DestinationType.mot, enabled: false),
    Destination(type: DestinationType.roadAssistance, enabled: false),
  ];

  static const _guestDestinations = [
    Destination(type: DestinationType.parking, enabled: false),
    Destination(type: DestinationType.mot),
    Destination(type: DestinationType.roadAssistance),
  ];

  @override
  StateNotifier<Newspaper?> get newspaper => _newspaper;
}
