import 'package:elementary/elementary.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/repository/auth/auth_repository.dart';
import 'package:sba/src/repository/content/content_repository.dart';
import 'package:sba/src/repository/content/model/newspaper.dart';
import 'package:sba/src/repository/subscription/model/subscription_data.dart';
import 'package:sba/src/repository/subscription/subscription_repository.dart';
import 'package:sba/src/repository/toll/model/toll_data.dart';
import 'package:sba/src/repository/toll/toll_repository.dart';
import 'package:sba/src/repository/user/model/type/user_type.dart';
import 'package:sba/src/repository/user/model/user_data.dart';
import 'package:sba/src/repository/user/user_repository.dart';
import 'package:sba/src/repository/vehicle/model/vehicle_data.dart';
import 'package:sba/src/repository/vehicle/vehicle_repository.dart';

class HomeModel extends ElementaryModel {
  HomeModel({
    required UserRepository userRepository,
    required AuthRepository authRepository,
    required VehicleRepository vehicleRepository,
    required ContentRepository contentRepository,
    required TollRepository tollRepository,
    required SubscriptionRepository subscriptionRepository,
    super.errorHandler,
  })  : _userRepository = userRepository,
        _authRepository = authRepository,
        _vehicleRepository = vehicleRepository,
        _contentRepository = contentRepository,
        _tollRepository = tollRepository,
        _subscriptionRepository = subscriptionRepository;

  final UserRepository _userRepository;
  final AuthRepository _authRepository;
  final VehicleRepository _vehicleRepository;
  final ContentRepository _contentRepository;
  final TollRepository _tollRepository;
  final SubscriptionRepository _subscriptionRepository;

  Stream<UserData?> get user => _userRepository.user;

  Stream<UserType> get type => _userRepository.type;

  Stream<Result<List<VehicleData>>> get vehicles => _vehicleRepository.vehicles;

  Future<Newspaper?> get newspaper =>
      _contentRepository.getNewspaper().then((r) => r.maybeValue);

  Future<TollData?> validTollForVehicle(VehicleData data) =>
      _tollRepository.getValidTollForVehicle(data.plateNumber);

  Future<SubscriptionData?> subscriptionForVehicle(VehicleData data) =>
  _subscriptionRepository.subscriptionForVehicle(data);

  Future<Result<void>> logout() => _authRepository.logout();
}
