import 'package:elementary/elementary.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/repository/notifications/model/notification_data.dart';
import 'package:sba/src/repository/notifications/notification_repository.dart';

class NotificationModel extends ElementaryModel {
  NotificationModel({
    required NotificationRepository repository,
    super.errorHandler,
  }) : _repository = repository;

  final NotificationRepository _repository;

  Stream<Result<List<NotificationData>>> get notifications =>
      _repository.notifications;

  Future<Result<void>> seeAllNotifications() =>
      _repository.seeAllNotifications();
}
