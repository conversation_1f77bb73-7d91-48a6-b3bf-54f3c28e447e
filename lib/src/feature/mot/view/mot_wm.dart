import 'package:elementary/elementary.dart';
import 'package:elementary_helper/elementary_helper.dart';
import 'package:flutter/material.dart';
import 'package:rxdart/rxdart.dart';
import 'package:sba/src/app/di/di.dart';
import 'package:sba/src/common/extension/build_context_extension.dart';
import 'package:sba/src/common/external_launcher/external_launcher.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/feature/main/main_router.dart';
import 'package:sba/src/feature/mot/view/model/mot_tab.dart';
import 'package:sba/src/feature/mot/view/mot_model.dart';
import 'package:sba/src/feature/mot/view/mot_screen.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/avi/model/avi_booking_data.dart';
import 'package:sba/src/repository/avi/model/types/avi_status.dart';
import 'package:sba/src/ui/modal/message_dialog.dart';

abstract interface class IMotWidgetModel implements IWidgetModel {
  StateNotifier<List<MotTab>> get tabs;

  StateNotifier<List<AviBookingData>> get data;

  void requestMot();

  void cancelMot(AviBookingData data);

  void startNavigation(AviBookingData data);
}

MotWidgetModel defaultMotWidgetModelFactory(BuildContext context) {
  return MotWidgetModel(
    MotModel(
      aviRepository: get(),
      errorHandler: get(),
    ),
  );
}

class MotWidgetModel extends WidgetModel<MotScreen, MotModel>
    implements IMotWidgetModel {
  MotWidgetModel(super.model);

  final _tabs = StateNotifier<List<MotTab>>(initValue: _defaultTabs);
  final _motData = StateNotifier<List<AviBookingData>>();

  final _sub = CompositeSubscription();

  @override
  void initWidgetModel() async {
    super.initWidgetModel();

    _sub.add(
      model.bookings
          .asyncMap((result) async {
            if (result is Failure) {
              await context.showGeneralErrorDialog(failure: result as Failure);
            }

            return result;
          })
          .map((result) => result.maybeValue)
          .listen(_motData.accept),
    );
  }

  @override
  void dispose() {
    _sub.dispose();
    super.dispose();
  }

  @override
  void cancelMot(AviBookingData data) async {
    Future<void> delete(AviBookingData data) async {
      await context.showLoadingDialog();
      final result = await model.cancelBooking(data);
      context.hideLoadingDialog();

      if (result is Failure) {
        await context.showGeneralErrorDialog(failure: result);
      }
    }

    await context.showMessageDialog(
      builder: (BuildContext context) => MessageDialog.action(
        type: MessageDialogType.error,
        title: context.l10n.message_cancel_title,
        text: context.l10n.message_delete_request_text,
        primaryActionText: context.l10n.action_yes,
        secondaryActionText: context.l10n.action_no,
        primaryAction: () => delete(data),
      ),
    );
  }

  @override
  void requestMot() {
    const MotRequestRoute().go(context);
  }

  @override
  void startNavigation(AviBookingData data) {
    ExternalLauncher.launchNavigation(data.locationDetails.coordinates);
  }

  @override
  StateNotifier<List<AviBookingData>> get data => _motData;

  @override
  StateNotifier<List<MotTab>> get tabs => _tabs;
}

const List<MotTab> _defaultTabs = [
  MotTab(type: MotTabType.active, status: {AviStatus.active}),
  MotTab(
    type: MotTabType.cancelled,
    status: {AviStatus.cancelled, AviStatus.rejected},
  ),
  MotTab(type: MotTabType.completed, status: {AviStatus.completed}),
];
