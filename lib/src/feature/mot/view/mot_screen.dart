import 'package:elementary/elementary.dart';
import 'package:elementary_helper/elementary_helper.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:sba/src/feature/mot/view/mot_wm.dart';
import 'package:sba/src/feature/mot/view/widget/mot_tile.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/avi/model/avi_booking_data.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/theme/theme_extension.dart';
import 'package:sba/src/ui/widget/action_box.dart';
import 'package:sba/src/ui/widget/sliver_title.dart';
import 'package:sba/src/ui/widget/smart_list_view.dart';

class MotScreen extends ElementaryWidget<IMotWidgetModel> {
  const MotScreen({
    Key? key,
    WidgetModelFactory wmFactory = defaultMotWidgetModelFactory,
  }) : super(wmFactory, key: key);

  @override
  Widget build(IMotWidgetModel wm) {
    return DoubleSourceBuilder(
      firstSource: wm.data,
      secondSource: wm.tabs,
      builder: (context, data, tabs) {
        return DefaultTabController(
          length: tabs?.length ?? 0,
          child: Scaffold(
            body: NestedScrollView(
              headerSliverBuilder: (context, _) => [
                SliverTitle(
                  text: context.l10n.navigation_mot,
                  padding: UISpacing.defaultElementPaddingWithoutBottom,
                  action: data?.isEmpty ?? true
                      ? null
                      : FloatingActionButton.small(
                          onPressed: wm.requestMot,
                          child: const Icon(Icons.add),
                        ),
                ),
                const SliverGap(UISpacing.m),
                PinnedHeaderSliver(
                  child: data?.isNotEmpty ?? true
                      ? Container(
                          padding: UISpacing.defaultElementHorizontalPadding,
                          color: context.theme.colorScheme.surface,
                          child: TabBar(
                            tabs: tabs
                                    ?.map(
                                      (it) => Text(
                                        it.formattedTitle(context),
                                        textAlign: TextAlign.center,
                                      ),
                                    )
                                    .toList() ??
                                List.empty(),
                          ),
                        )
                      : const SizedBox.shrink(),
                ),
              ],
              body: DoubleSourceBuilder(
                firstSource: wm.data,
                secondSource: wm.tabs,
                builder: (context, data, tabs) => data?.isEmpty ?? false
                    ? Padding(
                        padding: UISpacing.defaultScreenPadding,
                        child: Column(
                          children: [
                            ActionBox(
                              icon: Icons.add,
                              text: context.l10n.action_reserve_mot,
                              onTap: wm.requestMot,
                            ),
                          ],
                        ),
                      )
                    : TabBarView(
                        children: tabs
                                ?.map(
                                  (e) => SmartListView(
                                    loading: data == null,
                                    data: data?.where((it) => e.status.contains(it.state)).toList(),
                                    skeletonData: List.filled(1, AviBookingData.fake()),
                                    padding: UISpacing.defaultScreenPadding,
                                    separator: const Gap(UISpacing.m),
                                    emptyText: context.l10n.empty_request,
                                    builder: (context, data) => MotTile(
                                      data: data,
                                      onDelete: () => wm.cancelMot(data),
                                      onNavigate: () => wm.startNavigation(data),
                                    ),
                                  ),
                                )
                                .toList() ??
                            List.empty(),
                      ),
              ),
            ),
          ),
        );
      },
    );
  }
}
