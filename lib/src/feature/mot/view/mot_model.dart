import 'package:elementary/elementary.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/repository/avi/avi_repository.dart';
import 'package:sba/src/repository/avi/model/avi_booking_data.dart';

class MotModel extends ElementaryModel {
  MotModel({required AVIRepository aviRepository, super.errorHandler}) : _aviRepository = aviRepository;

  final AVIRepository _aviRepository;

  Stream<Result<List<AviBookingData>>> get bookings => _aviRepository.bookings;
  Future<Result<void>> cancelBooking(AviBookingData data) => _aviRepository.cancelBooking(data);
}
