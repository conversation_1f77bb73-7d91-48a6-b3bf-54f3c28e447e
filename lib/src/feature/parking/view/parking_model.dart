import 'package:collection/collection.dart';
import 'package:elementary/elementary.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/common/result/result_extension.dart';
import 'package:sba/src/repository/parking/model/parking_request.dart';
import 'package:sba/src/repository/parking/parking_repository.dart';

class ParkingModel extends ElementaryModel {
  ParkingModel({
    required ParkingRepository parkingRepository,
    super.errorHandler,
  }) : _parkingRepository = parkingRepository;

  final ParkingRepository _parkingRepository;

  Stream<Result<List<ParkingRequest>>> get parkingRequests =>
      _parkingRepository.requestsStream.map(
        (res) => res.map(
          (list) => list
              .where((e) => e.id != null)
              .groupListsBy((e) => (e.zone?.id, e.plateNumber))
              .values
              .map((e) => e.reduce((o1, o2) => o1.id! > o2.id! ? o1 : o2))
              .toList(),
        ),
      );
}
