import 'package:collection/collection.dart';
import 'package:elementary/elementary.dart';
import 'package:elementary_helper/elementary_helper.dart';
import 'package:flutter/material.dart';
import 'package:rxdart/rxdart.dart';
import 'package:sba/src/app/di/di.dart';
import 'package:sba/src/common/extension/build_context_extension.dart';
import 'package:sba/src/common/external_launcher/external_launcher.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/feature/main/main_router.dart';
import 'package:sba/src/feature/subscription/view/subscription_model.dart';
import 'package:sba/src/feature/subscription/view/subscription_screen.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/payment/model/payment_status.dart';
import 'package:sba/src/repository/subscription/model/subscription_data.dart';
import 'package:sba/src/repository/subscription/model/subscription_request_data.dart';
import 'package:toastification/toastification.dart';

const _activateUrl = 'https://www.sba.bg/activate';

abstract interface class ISubscriptionWidgetModel implements IWidgetModel {
  void onActivateTap();

  void onBuyTap();

  void onSubscriptionTap(SubscriptionData data);

  void onPaymentTap(SubscriptionRequestData data);

  StateNotifier<List<SubscriptionData>> get subscriptions;

  StateNotifier<List<SubscriptionRequestData>> get requests;
}

SubscriptionWidgetModel defaultSubscriptionWidgetModelFactory(
  BuildContext context,
) {
  return SubscriptionWidgetModel(
    SubscriptionModel(
      errorHandler: get(),
      repository: get(),
    ),
  );
}

class SubscriptionWidgetModel
    extends WidgetModel<SubscriptionScreen, SubscriptionModel>
    implements ISubscriptionWidgetModel {
  SubscriptionWidgetModel(super.model);

  final _subscriptions = StateNotifier<List<SubscriptionData>>();
  final _requests = StateNotifier<List<SubscriptionRequestData>>();

  final _sub = CompositeSubscription();

  @override
  void initWidgetModel() {
    _sub
      ..add(
        model.subscriptions
            .asyncMap((result) async {
              if (result is Failure) {
                await context.showGeneralErrorDialog(
                  failure: result as Failure,
                );
              }

              return result;
            })
            .map((e) => e.maybeValue)
            .listen(_subscriptions.accept),
      )
      ..add(
        model.requests
            .asyncMap((result) async {
              if (result is Failure) {
                await context.showGeneralErrorDialog(
                  failure: result as Failure,
                );
              }

              return result;
            })
            .map((e) => e.maybeValue)
            .map((e) => e?.sorted((a, b) => b.id!.compareTo(a.id!)))
            .listen(requests.accept),
      );
    super.initWidgetModel();
  }

  @override
  void dispose() {
    _sub.dispose();
    super.dispose();
  }

  @override
  void onActivateTap() {
    ExternalLauncher.uri(_activateUrl);
  }

  @override
  void onBuyTap() {
    const BuySubscriptionRoute().go(context);
  }

  @override
  void onSubscriptionTap(SubscriptionData data) {
    SubscriptionDetailsRoute(subscriptionId: data.id).push<void>(context);
  }

  @override
  void onPaymentTap(SubscriptionRequestData data) async {
    await context.showLoadingDialog();
    final result = await model.payRequest(data);
    context.hideLoadingDialog();

    if (result is Failure) {
      await context.showGeneralErrorDialog(failure: result as Failure);
    }

    if (result.maybeValue?.isPaid ?? false) {
      context.showToast(
        type: ToastificationType.success,
        title: context.l10n.message_success_paid,
      );
    }
  }

  @override
  StateNotifier<List<SubscriptionData>> get subscriptions => _subscriptions;

  @override
  StateNotifier<List<SubscriptionRequestData>> get requests => _requests;
}
