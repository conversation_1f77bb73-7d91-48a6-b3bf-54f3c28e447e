import 'package:elementary/elementary.dart';
import 'package:elementary_helper/elementary_helper.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:sba/src/feature/subscription/details/widget/subscription_tile.dart';
import 'package:sba/src/feature/subscription/view/subscription_wm.dart';
import 'package:sba/src/feature/subscription/view/widget/subscription_request_tile.dart';
import 'package:sba/src/generated/assets.gen.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/subscription/model/subscription_data.dart';
import 'package:sba/src/repository/subscription/model/subscription_request_data.dart';
import 'package:sba/src/ui/theme/theme_extension.dart';
import 'package:sba/src/ui/theme/ui_spacing.dart';
import 'package:sba/src/ui/widget/navigation_box.dart';
import 'package:sba/src/ui/widget/sliver_title.dart';
import 'package:sba/src/ui/widget/smart_list_view.dart';

enum _Tabs { cards, requests }

extension _TabsExtension on _Tabs {
  String localizedName(BuildContext context) => switch (this) {
        _Tabs.cards => context.l10n.subscription_tab_cards,
        _Tabs.requests => context.l10n.subscription_tab_requests
      };
}

class SubscriptionScreen extends ElementaryWidget<ISubscriptionWidgetModel> {
  const SubscriptionScreen({
    Key? key,
    WidgetModelFactory wmFactory = defaultSubscriptionWidgetModelFactory,
  }) : super(wmFactory, key: key);

  @override
  Widget build(ISubscriptionWidgetModel wm) {
    return DefaultTabController(
      length: _Tabs.values.length,
      child: Scaffold(
        body: NestedScrollView(
          headerSliverBuilder: (context, _) => [
            SliverTitle(
              text: context.l10n.navigation_subscription,
              padding: UISpacing.defaultElementPaddingWithoutBottom,
            ),
            const SliverGap(UISpacing.xl),
            SliverPadding(
              padding: UISpacing.defaultElementHorizontalPadding,
              sliver: SliverGrid.count(
                crossAxisCount: 2,
                mainAxisSpacing: UISpacing.s,
                crossAxisSpacing: UISpacing.s,
                childAspectRatio: 17 / 10,
                children: [
                  NavigationBox(
                    icon: Assets.icons.toggleOn.svg(),
                    text: context.l10n.navigation_activate_subscription,
                    onTap: wm.onActivateTap,
                  ),
                  NavigationBox(
                    icon: Assets.icons.featuredPlayList.svg(),
                    text: context.l10n.navigation_buy_subscription,
                    onTap: wm.onBuyTap,
                  ),
                ],
              ),
            ),
            const SliverGap(UISpacing.m),
            PinnedHeaderSliver(
              child: Container(
                padding: UISpacing.defaultElementHorizontalPadding,
                color: context.theme.colorScheme.surface,
                child: TabBar(
                  tabs: _Tabs.values
                      .map(
                        (it) => Text(
                          it.localizedName(context),
                          textAlign: TextAlign.center,
                        ),
                      )
                      .toList(),
                ),
              ),
            ),
          ],
          body: TabBarView(
            children: [
              StateNotifierBuilder(
                listenableState: wm.subscriptions,
                builder: (context, data) => SmartListView(
                  loading: data == null,
                  data: data,
                  padding: UISpacing.defaultScreenPadding,
                  separator: const Gap(UISpacing.m),
                  skeletonData: List.filled(2, SubscriptionData.fake()),
                  emptyText: context.l10n.empty_cards,
                  builder: (context, data) => SubscriptionTile(
                    data: data,
                    onTap: () => wm.onSubscriptionTap(data),
                  ),
                ),
              ),
              StateNotifierBuilder(
                listenableState: wm.requests,
                builder: (context, data) => SmartListView(
                  loading: data == null,
                  data: data,
                  padding: UISpacing.defaultScreenPadding,
                  separator: const Gap(UISpacing.m),
                  emptyText: context.l10n.empty_request,
                  skeletonData: List.filled(2, SubscriptionRequestData.fake()),
                  builder: (context, data) => SubscriptionRequestTile(
                    data: data,
                    onPayTap: () => wm.onPaymentTap(data),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
