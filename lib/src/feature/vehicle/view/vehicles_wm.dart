import 'package:elementary/elementary.dart';
import 'package:elementary_helper/elementary_helper.dart';
import 'package:flutter/material.dart';
import 'package:rxdart/rxdart.dart';
import 'package:sba/src/app/di/di.dart';
import 'package:sba/src/common/extension/build_context_extension.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/feature/main/main_router.dart';
import 'package:sba/src/feature/vehicle/view/vehicles_model.dart';
import 'package:sba/src/feature/vehicle/view/vehicles_screen.dart';
import 'package:sba/src/repository/vehicle/model/vehicle_data.dart';

abstract interface class IVehiclesWidgetModel implements IWidgetModel {
  StateNotifier<List<VehicleData>> get vehicles;

  void onVehicleTap(VehicleData data);

  void onAddVehicleTap();
}

VehiclesWidgetModel defaultVehiclesWidgetModelFactory(BuildContext context) {
  return VehiclesWidgetModel(VehiclesModel(vehicleRepository: get()));
}

class VehiclesWidgetModel extends WidgetModel<VehiclesScreen, VehiclesModel>
    implements IVehiclesWidgetModel {
  VehiclesWidgetModel(super.model);

  final _sub = CompositeSubscription();

  final StateNotifier<List<VehicleData>> _vehicles = StateNotifier();

  @override
  void initWidgetModel() {
    _sub.add(
      model.vehicles
          .asyncMap((result) async {
            if (result is Failure) {
              await context.showGeneralErrorDialog(
                failure: result as Failure,
              );
            }

            return result;
          })
          .map((e) => e.maybeValue)
          .listen(_vehicles.accept),
    );
    super.initWidgetModel();
  }

  @override
  void dispose() {
    _sub.dispose();
    super.dispose();
  }

  @override
  void onAddVehicleTap() {
    const NewVehicleRoute().go(context);
  }

  @override
  void onVehicleTap(VehicleData data) {
    EditVehicleRoute(id: data.id!).go(context);
  }

  @override
  StateNotifier<List<VehicleData>> get vehicles => _vehicles;
}
