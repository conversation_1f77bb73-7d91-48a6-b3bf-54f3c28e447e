import 'package:elementary/elementary.dart';
import 'package:elementary_helper/elementary_helper.dart';
import 'package:flutter/material.dart';
import 'package:sba/src/feature/vehicle/view/vehicles_wm.dart';
import 'package:sba/src/feature/vehicle/view/widget/vehicle_tile.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/vehicle/model/vehicle_data.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/widget/action_box.dart';
import 'package:sba/src/ui/widget/sliver_title.dart';
import 'package:sba/src/ui/widget/smart_grid_view.dart';

class VehiclesScreen extends ElementaryWidget<IVehiclesWidgetModel> {
  const VehiclesScreen({
    Key? key,
    WidgetModelFactory wmFactory = defaultVehiclesWidgetModelFactory,
  }) : super(wmFactory, key: key);

  @override
  Widget build(IVehiclesWidgetModel wm) {
    return Scaffold(
      body: NestedScrollView(
        headerSliverBuilder: (context, _) => [
          SliverTitle(
            text: context.l10n.navigation_vehicles,
            padding: UISpacing.defaultElementPaddingWithoutBottom,
          ),
        ],
        body: StateNotifierBuilder(
          listenableState: wm.vehicles,
          builder: (context, data) => SmartGridView(
            loading: data == null,
            data: data,
            skeletonData: List.filled(2, VehicleData.fake()),
            emptyText: context.l10n.empty_vehicles,
            padding: UISpacing.defaultScreenPadding,
            delegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              mainAxisSpacing: UISpacing.s,
              crossAxisSpacing: UISpacing.s,
            ),
            builder: (context, data) => VehicleTile(
              data: data,
              onTap: () => wm.onVehicleTap(data),
            ),
            action: ActionBox(
              icon: Icons.add,
              text: context.l10n.action_add_vehicle,
              onTap: wm.onAddVehicleTap,
            ),
          ),
        ),
      ),
    );
  }
}
