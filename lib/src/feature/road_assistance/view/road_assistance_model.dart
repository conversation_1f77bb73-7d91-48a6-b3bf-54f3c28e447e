import 'package:elementary/elementary.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/repository/road_assistance/model/road_assistance_request.dart';
import 'package:sba/src/repository/road_assistance/road_assistance_repository.dart';

class RoadAssistanceModel extends ElementaryModel {
  RoadAssistanceModel({
    required RoadAssistanceRepository repository,
    super.errorHandler,
  }) : _repository = repository;
  final RoadAssistanceRepository _repository;

  Stream<Result<List<RoadAssistanceRequest>>> get requests =>
      _repository.requests;

  void refresh() => _repository.refresh();

  Future<Result<void>> cancelRequest(RoadAssistanceRequest data) =>
      _repository.cancelRequest(data);
}
