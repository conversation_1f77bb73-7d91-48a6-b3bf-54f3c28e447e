import 'package:collection/collection.dart';
import 'package:logger/logger.dart';
import 'package:rxdart/rxdart.dart';
import 'package:sba/src/api/model/toll/toll_dto.dart';
import 'package:sba/src/api/toll_api.dart';
import 'package:sba/src/common/extension/list_extension.dart';
import 'package:sba/src/common/result/call_with_result.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/common/result/result_extension.dart';
import 'package:sba/src/payment/payment_processor.dart';
import 'package:sba/src/repository/auth/auth_repository.dart';
import 'package:sba/src/repository/toll/model/toll_check_data.dart';
import 'package:sba/src/repository/toll/model/toll_country.dart';
import 'package:sba/src/repository/toll/model/toll_data.dart';
import 'package:sba/src/repository/toll/model/toll_product.dart';
import 'package:sba/src/repository/toll/model/toll_register_data.dart';
import 'package:sba/src/repository/user/model/type/user_type.dart';
import 'package:sba/src/repository/user/user_repository.dart';
import 'package:sba/src/repository/vehicle/vehicle_repository.dart';

const _tag = 'TollRepository';

final class TollRepository {
  TollRepository({
    required Logger logger,
    required TollApi tollApi,
    required AuthRepository authRepository,
    required UserRepository userRepository,
    required VehicleRepository vehicleRepository,
    required PaymentProcessor paymentProcessor,
  }) : _logger = logger,
       _tollApi = tollApi,
       _authRepository = authRepository,
       _userRepository = userRepository,
       _vehicleRepository = vehicleRepository {
    _authRepository.loggedInStream
        .doOnData((_) => _cachedResponse.add(null))
        .publish()
        .connect();

    _tollStream =
        _cachedResponse
            .distinct()
            .switchMap(
              (e) => e != null
                  ? e.isFailure
                        ? _retryTollData()
                        : Stream.value(e)
                  : _authRepository.loggedInStream
                        .where((e) => e)
                        .asyncMap((e) => _getTollData())
                        .doOnData(_cachedResponse.add),
            )
            .publishReplay(maxSize: 1)
          ..connect();
  }

  final Logger _logger;
  final TollApi _tollApi;
  final AuthRepository _authRepository;
  final UserRepository _userRepository;
  final VehicleRepository _vehicleRepository;
  final _cachedResponse = BehaviorSubject<Result<List<TollData>>?>.seeded(null);
  late final Stream<Result<List<TollData>>> _tollStream;

  Stream<Result<List<TollData>>> get toll => _tollStream;

  Future<TollData?> getValidTollForVehicle(String plateNumber) => _tollStream
      .map((e) => e.maybeValue)
      .map((e) => e?.where((i) => i.plateNumber == plateNumber))
      .map((e) => e?.firstWhereOrNull((e) => e.isValid))
      .first;

  Future<Result<List<TollCountry>>> getCountries() async {
    try {
      final result = await callWithCachedResult(_tollApi.getCountries);
      return result.listMap(TollCountry.fromDto);
    } catch (e) {
      _logger.e('$_tag: $e');
      return Result.otherError(e);
    }
  }

  Future<Result<List<TollProduct>>> getProducts() async {
    try {
      final result = await callWithCachedResult(_tollApi.getProducts);
      return result.listMap(TollProduct.fromDto);
    } catch (e) {
      _logger.e('$_tag: $e');
      return Result.otherError(e);
    }
  }

  Future<Result<List<TollData>>> _getTollData() async {
    try {
      final userType = await _userRepository.type.first;

      final result = switch (userType) {
        UserType.normal => await callWithResult(_tollApi.getUserToll),
        UserType.guest => Result.success(List<TollDto>.empty()),
      };

      final mapped = result.listMap(
        TollData.fromDto,
        growable: true,
      );

      return mapped;
    } catch (e) {
      _logger.e('$_tag: $e');
      return Result.otherError(e);
    }
  }

  Future<Result<TollCheckData?>> checkToll({
    required TollCountry country,
    required String plateNumber,
  }) async {
    try {
      final result = await callWithResult(
        () => _tollApi.tollCheck(
          countryCode: country.code,
          plateNumber: plateNumber,
        ),
      );

      if (result.isFailure) {
        return (result as Failure).transform();
      }

      final vehicle = await _vehicleRepository.getVehicleByPlateNumber(
        plateNumber,
      );

      final user = await _userRepository.getUser();

      final check = result.maybeValue;

      if (user == null) {
        return Result.otherError('User is null');
      }

      if (check == null) {
        return Result.otherError('Toll check is null');
      }

      if (vehicle != null && check.data.valid) {
        final updateResult = await callWithResult(
          () => _tollApi.updateTollCheck(
            dto: TollData.fromCheckData(
              user,
              country,
              plateNumber,
              check,
            ).toDto(),
          ),
        );

        if (updateResult.isSuccess) {
          final data = updateResult.map(TollData.fromDto).maybeValue!;
          var list = _cachedResponse.valueOrNull?.maybeValue?.asCopy();
          list = list?.replaceWhereOrAdd((e) => e.id == data.id, data);
          _cachedResponse.add(Result.success(list ?? List.empty()));
        }
      }

      return result.map((e) => TollCheckData.fromDto(e.data));
    } catch (e) {
      _logger.e('$_tag: $e');
      return Result.otherError(e);
    }
  }

  Future<Result<TollData>> registerToll(TollRegisterData data) async {
    try {
      final user = await _userRepository.getUser();

      if (user == null) {
        return Result.otherError('User is null');
      }

      final result = await callWithTollResult(
        () => _tollApi.registerToll(
          request: [
            data.toDto(),
          ],
        ),
      );

      if (result.isFailure) {
        return (result as Failure).transform();
      }

      final addResult = await callWithResult(
        () => _tollApi.addToll(
          dto: TollData.fromRegisterData(user, result.maybeValue!).toDto(),
        ),
      );

      return addResult.map(TollData.fromDto);
    } catch (e) {
      _logger.e('$_tag: $e');
      return Result.otherError(e);
    }
  }

  Future<Result<TollData>> activateToll(TollData data) async {
    return Result.otherError('Not implemented');
    /*try {
      final pendingData = _pendingPayments[data];

      if (pendingData?.status != PaymentStatus.authorized) {
        final paymentResult = pendingData?.status.isEligibleForRetry ?? false
            ? await _paymentProcessor.startPaymentFlow(pendingData!.orderId)
            : await _paymentProcessor.makePayment(data.price.minimalCurrency);

        if (paymentResult.isFailure) {
          return (paymentResult as Failure).transform();
        }

        final paymentData = paymentResult.maybeValue!;
        _pendingPayments[data] = paymentData;

        if (paymentData.status != PaymentStatus.authorized) {
          return Result.canceled(data: data);
        }
      }

      final activateResult = await callWithTollResult(
        () => _tollApi.activateToll(id: data.vignetteId),
      );

      if (activateResult.isFailure) {
        return (activateResult as Failure).transform();
      }

      final activated =
          TollData.fromActivateData(data, activateResult.maybeValue!);

      final updateResult = await callWithResult(
        () => _tollApi.updateToll(
          dto: activated.toDto(),
        ),
      );

      if (updateResult.isSuccess) {
        final list = _cachedResponse.valueOrNull?.maybeValue?.asCopy();
        list?.add(activated);
        _cachedResponse.add(Result.success(list ?? List.empty()));
        _pendingPayments.remove(data);
      }

      return updateResult.map((e) => activated);
    } catch (e) {
      _logger.e('$_tag: $e');
      return Result.otherError(e);
    }*/
  }
}
